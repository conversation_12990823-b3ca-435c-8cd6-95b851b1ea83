# Live Bidding with Socket.IO Integration

This document explains how to set up and test the Socket.IO live bidding functionality that has been integrated into your auction application.

## 🚀 Quick Start

### 1. Install Backend Dependencies

```bash
cd server
npm install
```

### 2. Start the Socket.IO Server

```bash
cd server
npm run dev
```

The server will start on `http://localhost:3001`

### 3. Start the Frontend

In a new terminal:

```bash
npm run dev
```

The frontend will start on `http://localhost:8080`

## 🔧 What's Been Implemented

### Frontend Changes

1. **Socket.IO Client Integration**
   - Added `socket.io-client` dependency
   - Created `src/lib/socket.ts` - Socket service singleton
   - Created `src/hooks/useSocket.ts` - React hook for Socket.IO
   - Created `src/lib/contexts/SocketContext.tsx` - Global Socket context

2. **Real-time Bidding**
   - Updated `src/pages/ReelDetail.tsx` to use Socket.IO
   - Modified `src/components/BiddingPanel.tsx` for live bidding
   - Added connection status indicators
   - Real-time bid updates across all connected users

3. **Enhanced UI**
   - Connection status indicators (Live/Offline)
   - Real-time bid notifications
   - Disabled bidding when offline
   - Optimistic UI updates

### Backend Implementation

1. **Socket.IO Server** (`server/server.js`)
   - Express.js server with Socket.IO
   - CORS configuration for frontend connection
   - Auction room management
   - Real-time bid broadcasting

## 📡 Socket.IO Events

### Client → Server

- `subscribe_auction` - Join an auction room
  ```javascript
  socket.emit('subscribe_auction', 'auction_id');
  ```

- `place_bid` - Submit a new bid
  ```javascript
  socket.emit('place_bid', {
    auctionId: 'auction_id',
    username: 'john_doe',
    price: 1500
  });
  ```

### Server → Client

- `new_bid` - Receive real-time bid updates
  ```javascript
  socket.on('new_bid', (data) => {
    console.log('New bid:', data.bid);
  });
  ```

- `auction_update` - Receive auction state updates
- `bid_error` - Receive bid validation errors

## 🧪 Testing the Integration

### Test Scenario 1: Single User Bidding

1. Open the frontend at `http://localhost:8080`
2. Navigate to any auction (e.g., `/reel/fe1`)
3. Check that the connection status shows "Live" (green)
4. Set a username if prompted
5. Place a bid using quick bid buttons or custom amount
6. Verify the bid appears in the bid history

### Test Scenario 2: Multi-User Real-time Bidding

1. Open two browser windows/tabs to the same auction
2. Set different usernames in each window
3. Place bids from one window
4. Verify that the other window receives real-time updates:
   - New bid appears in bid history
   - Current bid amount updates
   - Toast notification shows for bids from other users

### Test Scenario 3: Connection Status

1. Stop the backend server (`Ctrl+C`)
2. Refresh the frontend
3. Verify connection status shows "Offline" (red)
4. Try to place a bid - should show connection error
5. Restart the server
6. Verify connection status returns to "Live"

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
VITE_SOCKET_URL=http://localhost:3001
```

### Backend Configuration

The server can be configured via environment variables:

```env
PORT=3001
```

## 🐛 Troubleshooting

### Common Issues

1. **Connection Failed**
   - Ensure backend server is running on port 3001
   - Check CORS configuration in `server/server.js`
   - Verify `VITE_SOCKET_URL` environment variable

2. **Bids Not Updating**
   - Check browser console for Socket.IO errors
   - Verify auction ID matches between frontend and backend
   - Ensure user has subscribed to the auction room

3. **Multiple Connections**
   - Each browser tab creates a separate Socket.IO connection
   - This is expected behavior for testing multi-user scenarios

### Debug Mode

Enable Socket.IO debug logging:

```javascript
localStorage.debug = 'socket.io-client:socket';
```

## 🚀 Production Deployment

### Backend Deployment

1. Set production environment variables
2. Configure CORS for your production domain
3. Use a process manager like PM2
4. Set up SSL/TLS for secure WebSocket connections

### Frontend Deployment

1. Update `VITE_SOCKET_URL` to your production backend URL
2. Build the application: `npm run build`
3. Deploy to your hosting platform

## 📝 Next Steps

1. **Authentication**: Add user authentication for secure bidding
2. **Persistence**: Connect to a database for bid storage
3. **Rate Limiting**: Implement bid rate limiting per user
4. **Auction Management**: Add auction creation and management features
5. **Mobile Support**: Test and optimize for mobile devices

## 🎯 Key Features Implemented

✅ Real-time bidding across multiple users  
✅ Connection status indicators  
✅ Auction room subscription/unsubscription  
✅ Bid validation and error handling  
✅ Optimistic UI updates  
✅ Toast notifications for new bids  
✅ Offline state handling  
✅ Socket.IO event system matching your specification  

The integration is now complete and ready for testing!
