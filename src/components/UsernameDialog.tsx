import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useUsername } from "@/lib/contexts/UsernameContext";
import { toast } from "@/hooks/use-toast";

interface UsernameDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  required?: boolean;
}

export const UsernameDialog: React.FC<UsernameDialogProps> = ({
  open,
  onOpenChange,
  required = false,
}) => {
  const { username, setUsername, flightNumber, setFlightNumber } = useUsername();
  const [inputValue, setInputValue] = useState(username);
  const [flightInputValue, setFlightInputValue] = useState(flightNumber);

  const handleSave = () => {
    const trimmedValue = inputValue.trim();
    const trimmedFlightValue = flightInputValue.trim();

    if (!trimmedValue) {
      toast({
        title: "Username Required",
        description: "Please enter a username to continue.",
        variant: "destructive",
      });
      return;
    }

    if (trimmedValue.length < 2) {
      toast({
        title: "Username Too Short",
        description: "Username must be at least 2 characters long.",
        variant: "destructive",
      });
      return;
    }

    if (trimmedValue.length > 20) {
      toast({
        title: "Username Too Long",
        description: "Username must be 20 characters or less.",
        variant: "destructive",
      });
      return;
    }

    // Validate flight number format (optional but if provided, should be valid)
    if (trimmedFlightValue && !/^[A-Z]{2,3}[0-9]{1,4}[A-Z]?$/i.test(trimmedFlightValue)) {
      toast({
        title: "Invalid Flight Number",
        description: "Please enter a valid flight number (e.g., AA123, BA456).",
        variant: "destructive",
      });
      return;
    }

    setUsername(trimmedValue);
    setFlightNumber(trimmedFlightValue);
    onOpenChange(false);

    const description = trimmedFlightValue
      ? `Your username has been set to "${trimmedValue}" and flight number to "${trimmedFlightValue}".`
      : `Your username has been set to "${trimmedValue}".`;

    toast({
      title: "Profile Updated",
      description,
    });
  };

  const handleCancel = () => {
    if (required && !username) {
      toast({
        title: "Username Required",
        description: "You need to set a username to place bids.",
        variant: "destructive",
      });
      return;
    }
    setInputValue(username);
    setFlightInputValue(flightNumber);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={required ? undefined : onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {username ? "Update Profile" : "Set Your Profile"}
          </DialogTitle>
          <DialogDescription>
            {required
              ? "You need to set a username before you can place bids. Flight number is optional."
              : "Choose a username that will be displayed when you place bids. You can also add your flight number for route sharing."}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="username" className="text-right">
              Username
            </Label>
            <Input
              id="username"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Enter your username"
              className="col-span-3"
              maxLength={20}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSave();
                }
              }}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="flightNumber" className="text-right">
              Flight Number
            </Label>
            <Input
              id="flightNumber"
              value={flightInputValue}
              onChange={(e) => setFlightInputValue(e.target.value)}
              placeholder="e.g., AA123, BA456 (optional)"
              className="col-span-3"
              maxLength={10}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSave();
                }
              }}
            />
          </div>
        </div>
        <DialogFooter>
          {!required && (
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          )}
          <Button onClick={handleSave}>
            {username ? "Update Profile" : "Set Profile"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
