import { io, Socket } from 'socket.io-client';
import { SocketBidData, SocketNewBidData, SocketAuctionEnded } from './types';
import { timeSyncService } from './timeSync';

class SocketService {
  private socket: Socket | null = null;
  private readonly serverUrl: string;

  constructor() {
    // Default to localhost:3001 for development
    // In production, this should be your actual backend URL
    this.serverUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';
  }

  connect(): Socket {
    if (this.socket?.connected) {
      return this.socket;
    }

    try {
      this.socket = io(this.serverUrl, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        autoConnect: true,
      });

      this.socket.on('connect', () => {
        console.log('Connected to Socket.IO server');
        // Initialize time sync service and start periodic sync
        timeSyncService.initialize();
        timeSyncService.startPeriodicSync(this.socket);
      });

      this.socket.on('disconnect', (reason) => {
        console.log('Disconnected from Socket.IO server:', reason);
      });

      this.socket.on('connect_error', (error) => {
        console.error('Socket.IO connection error:', error);
      });

      return this.socket;
    } catch (error) {
      console.error('Failed to create socket connection:', error);
      throw error;
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Subscribe to an auction room
  subscribeToAuction(auctionId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe_auction', auctionId);
      console.log(`Subscribed to auction: ${auctionId}`);
    }
  }

  // Unsubscribe from an auction room
  unsubscribeFromAuction(auctionId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe_auction', auctionId);
      console.log(`Unsubscribed from auction: ${auctionId}`);
    }
  }

  // Place a bid
  placeBid(bidData: SocketBidData): void {
    if (this.socket?.connected) {
      this.socket.emit('place_bid', bidData);
      console.log('Bid placed:', bidData);
    } else {
      console.error('Cannot place bid: Socket not connected');
    }
  }

  // Listen for new bids
  onNewBid(callback: (data: SocketNewBidData) => void): void {
    if (this.socket) {
      this.socket.on('new_bid', callback);
    }
  }

  // Remove new bid listener
  offNewBid(callback?: (data: SocketNewBidData) => void): void {
    if (this.socket) {
      if (callback) {
        this.socket.off('new_bid', callback);
      } else {
        this.socket.off('new_bid');
      }
    }
  }

  // Listen for auction updates (viewer count, status changes, etc.)
  onAuctionUpdate(callback: (data: any) => void): void {
    if (this.socket) {
      this.socket.on('auction_update', callback);
    }
  }

  // Remove auction update listener
  offAuctionUpdate(callback?: (data: any) => void): void {
    if (this.socket) {
      if (callback) {
        this.socket.off('auction_update', callback);
      } else {
        this.socket.off('auction_update');
      }
    }
  }

  // Listen for bid errors
  onBidError(callback: (data: { message: string }) => void): void {
    if (this.socket) {
      this.socket.on('bid_error', callback);
    }
  }

  // Remove bid error listener
  offBidError(callback?: (data: { message: string }) => void): void {
    if (this.socket) {
      if (callback) {
        this.socket.off('bid_error', callback);
      } else {
        this.socket.off('bid_error');
      }
    }
  }

  // Listen for auction ended events
  onAuctionEnded(callback: (data: SocketAuctionEnded) => void): (() => void) | undefined {
    if (this.socket) {
      this.socket.on('auction_ended', callback);
      return () => {
        if (this.socket) {
          this.socket.off('auction_ended', callback);
        }
      };
    }
    return undefined;
  }

  // Remove auction ended listener
  offAuctionEnded(callback?: (data: SocketAuctionEnded) => void): void {
    if (this.socket) {
      if (callback) {
        this.socket.off('auction_ended', callback);
      } else {
        this.socket.off('auction_ended');
      }
    }
  }

  // Perform time synchronization via socket
  performTimeSync(): void {
    if (this.socket?.connected) {
      timeSyncService.performImmediateSync(this.socket).catch(error => {
        console.error('Socket time sync failed:', error);
      });
    } else {
      timeSyncService.performImmediateSync().catch(error => {
        console.error('HTTP time sync failed:', error);
      });
    }
  }

  // Get synchronized server time
  getServerTime(): number {
    return timeSyncService.getServerTime();
  }

  // Check if time is synchronized
  isTimeSynchronized(): boolean {
    return timeSyncService.isSynchronized();
  }
}

// Create a singleton instance
const socketService = new SocketService();

export default socketService;
