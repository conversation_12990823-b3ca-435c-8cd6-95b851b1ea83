import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface UsernameContextType {
  username: string;
  setUsername: (username: string) => void;
  hasUsername: boolean;
  flightNumber: string;
  setFlightNumber: (flightNumber: string) => void;
  hasFlightNumber: boolean;
}

const UsernameContext = createContext<UsernameContextType | undefined>(undefined);

interface UsernameProviderProps {
  children: ReactNode;
}

export const UsernameProvider: React.FC<UsernameProviderProps> = ({ children }) => {
  const [username, setUsernameState] = useState<string>('');
  const [flightNumber, setFlightNumberState] = useState<string>('');

  // Load username and flight number from localStorage on mount
  useEffect(() => {
    const savedUsername = localStorage.getItem('auction-username');
    const savedFlightNumber = localStorage.getItem('auction-flight-number');
    if (savedUsername) {
      setUsernameState(savedUsername);
    }
    if (savedFlightNumber) {
      setFlightNumberState(savedFlightNumber);
    }
  }, []);

  const setUsername = (newUsername: string) => {
    setUsernameState(newUsername);
    if (newUsername.trim()) {
      localStorage.setItem('auction-username', newUsername.trim());
    } else {
      localStorage.removeItem('auction-username');
    }
  };

  const setFlightNumber = (newFlightNumber: string) => {
    setFlightNumberState(newFlightNumber);
    if (newFlightNumber.trim()) {
      localStorage.setItem('auction-flight-number', newFlightNumber.trim());
    } else {
      localStorage.removeItem('auction-flight-number');
    }
  };

  const hasUsername = username.trim().length > 0;
  const hasFlightNumber = flightNumber.trim().length > 0;

  return (
    <UsernameContext.Provider value={{
      username,
      setUsername,
      hasUsername,
      flightNumber,
      setFlightNumber,
      hasFlightNumber
    }}>
      {children}
    </UsernameContext.Provider>
  );
};

export const useUsername = () => {
  const context = useContext(UsernameContext);
  if (context === undefined) {
    throw new Error('useUsername must be used within a UsernameProvider');
  }
  return context;
};
