import { BackendAuctionData } from './types';

class AuctionService {
  private readonly baseUrl: string;

  constructor() {
    // Default to localhost:3001 for development
    // In production, this should be your actual backend URL
    this.baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
  }

  /**
   * Fetch auction data from the backend
   */
  async getAuction(auctionId: string): Promise<BackendAuctionData | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/auction/${auctionId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          console.warn(`Auction ${auctionId} not found on backend`);
          return null;
        }
        throw new Error(`Failed to fetch auction: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Error fetching auction ${auctionId}:`, error);
      return null;
    }
  }

  /**
   * Fetch all auctions from the backend
   */
  async getAllAuctions(): Promise<BackendAuctionData[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/auctions`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch auctions: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching all auctions:', error);
      return [];
    }
  }

  /**
   * Get server time for synchronization
   */
  async getServerTime(): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/api/time`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch server time: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data.serverTime;
    } catch (error) {
      console.error('Error fetching server time:', error);
      return Date.now(); // Fallback to local time
    }
  }
}

// Export singleton instance
export const auctionService = new AuctionService();
export default auctionService;
