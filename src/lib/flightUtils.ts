/**
 * Flight utilities for handling flight number validation and route information
 */

export interface FlightRoute {
  flightNumber: string;
  from: string;
  to: string;
  airline: string;
  status: 'found' | 'not_found';
}

/**
 * Validates flight number format
 * @param flightNumber - The flight number to validate
 * @returns boolean indicating if the format is valid
 */
export const validateFlightNumber = (flightNumber: string): boolean => {
  return /^[A-Z]{2,3}[0-9]{1,4}[A-Z]?$/i.test(flightNumber);
};

/**
 * Fetches flight route information from the server
 * @param flightNumber - The flight number to look up
 * @returns Promise with flight route information
 */
export const getFlightRoute = async (flightNumber: string): Promise<FlightRoute> => {
  try {
    const response = await fetch(`/api/flight/${encodeURIComponent(flightNumber)}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching flight route:', error);
    // Return a fallback response
    return {
      flightNumber: flightNumber.toUpperCase(),
      from: 'Unknown',
      to: 'Unknown',
      airline: 'Unknown',
      status: 'not_found'
    };
  }
};

/**
 * Formats flight route for display
 * @param route - The flight route object
 * @returns Formatted string for display
 */
export const formatFlightRoute = (route: FlightRoute): string => {
  if (route.status === 'not_found') {
    return `${route.flightNumber} (Route Unknown)`;
  }
  
  return `${route.flightNumber}: ${route.from} → ${route.to} (${route.airline})`;
};
