import { io } from 'socket.io-client';

// Connect to the server
const socket = io('http://localhost:3001');

socket.on('connect', () => {
  console.log('Connected to server');
  
  // Subscribe to auction fe8
  socket.emit('subscribe_auction', 'fe8');
  
  // Wait a moment then place a bid
  setTimeout(() => {
    console.log('Placing test bid...');
    socket.emit('place_bid', {
      auctionId: 'fe8',
      username: 'TestUser3',
      price: 1300,
      flightNumber: 'AA123'
    });
  }, 1000);
});

socket.on('new_bid', (data) => {
  console.log('New bid received:', data);
});

socket.on('bid_error', (data) => {
  console.log('Bid error:', data);
});

socket.on('auction_update', (data) => {
  console.log('Auction update:', data);
});

// Disconnect after 5 seconds
setTimeout(() => {
  console.log('Disconnecting...');
  socket.disconnect();
  process.exit(0);
}, 5000);
